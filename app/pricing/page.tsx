"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import Logo from "@/components/ui/logo";
import { motion } from "framer-motion";
import { ArrowRight, CheckCircle, Crown, Star, X, Zap } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

export default function PricingPage() {
  const [billingCycle, setBillingCycle] = useState<"monthly" | "annual">("monthly");

  const plans = [
    {
      name: "Starter",
      description: "Perfect for getting started",
      monthlyPrice: 0,
      annualPrice: 0,
      icon: Zap,
      color: "blue",
      popular: false,
      features: [
        "Basic financial health score",
        "Document upload (up to 5 files)",
        "Basic report generation",
        "Email support",
        "Community access"
      ],
      limitations: [
        "Limited to 1 business profile",
        "Basic scoring algorithm",
        "No investor matching",
        "No advisor calls"
      ]
    },
    {
      name: "Professional",
      description: "For growing businesses",
      monthlyPrice: 39,
      annualPrice: 390,
      icon: Star,
      color: "emerald",
      popular: true,
      features: [
        "Advanced financial analysis",
        "Unlimited document uploads",
        "Detailed reports & insights",
        "Investor matching",
        "Priority support",
        "Benchmark comparisons",
        "Monthly advisor call",
        "API access"
      ],
      limitations: [
        "Up to 3 business profiles",
        "Standard investor network"
      ]
    },
    {
      name: "Enterprise",
      description: "For established businesses",
      monthlyPrice: 129,
      annualPrice: 1290,
      icon: Crown,
      color: "amber",
      popular: false,
      features: [
        "Everything in Professional",
        "10X Growth Hack access",
        "Dedicated advisor calls",
        "Custom integrations",
        "White-label reports",
        "Premium investor network",
        "24/7 phone support",
        "Custom onboarding",
        "Advanced analytics",
        "Multi-user accounts"
      ],
      limitations: []
    }
  ];

  const formatPrice = (price: number) => {
    if (price === 0) return "Free";
    return `$${price.toLocaleString()}`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <header className="border-b border-slate-700/50 bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Logo href="/" size="md" showText={false} />
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/sme" className="text-slate-300 hover:text-white transition-colors">For SMEs</Link>
            <Link href="/investor" className="text-slate-300 hover:text-white transition-colors">For Investors</Link>
            <Link href="/consultant" className="text-slate-300 hover:text-white transition-colors">For Consultants</Link>
            <Link href="/10x-growth-hack" className="text-slate-300 hover:text-white transition-colors">10X Growth</Link>
            <Link href="/auth/signin" className="text-slate-300 hover:text-white transition-colors">Log in</Link>
            <Button className="bg-blue-600 hover:bg-blue-700 text-white rounded-full px-6">
              Get Started
            </Button>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Badge className="mb-6 px-6 py-2 text-sm font-medium bg-blue-500/10 text-blue-300 border-blue-500/20">
              💰 Transparent Pricing
            </Badge>
            <h1 className="text-5xl sm:text-6xl font-bold text-white mb-8 leading-tight">
              Choose Your
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-emerald-400">
                Growth Plan
              </span>
            </h1>
            <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto leading-relaxed">
              Start free and scale as you grow. All plans include our core financial evaluation features 
              with no hidden fees or long-term contracts.
            </p>

            {/* Billing Toggle */}
            <div className="flex items-center justify-center mb-12">
              <div className="bg-slate-800/50 rounded-full p-1 border border-slate-700/50">
                <button
                  onClick={() => setBillingCycle("monthly")}
                  className={`px-6 py-2 rounded-full text-sm font-medium transition-all ${
                    billingCycle === "monthly"
                      ? "bg-blue-600 text-white"
                      : "text-slate-400 hover:text-white"
                  }`}
                >
                  Monthly
                </button>
                <button
                  onClick={() => setBillingCycle("annual")}
                  className={`px-6 py-2 rounded-full text-sm font-medium transition-all ${
                    billingCycle === "annual"
                      ? "bg-blue-600 text-white"
                      : "text-slate-400 hover:text-white"
                  }`}
                >
                  Annual
                  <Badge className="ml-2 bg-emerald-500/20 text-emerald-400 text-xs">Save 17%</Badge>
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="py-20 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {plans.map((plan, index) => (
              <motion.div
                key={plan.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="relative"
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                    <Badge className="bg-emerald-500 text-white px-4 py-1">Most Popular</Badge>
                  </div>
                )}
                <Card className={`h-full ${
                  plan.popular 
                    ? "bg-slate-800/50 border-emerald-500/50 scale-105" 
                    : "bg-slate-800/50 border-slate-700/50 hover:border-blue-500/50"
                } transition-all duration-300`}>
                  <CardContent className="p-8">
                    <div className="text-center mb-8">
                      <div className={`w-16 h-16 bg-${plan.color}-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4`}>
                        <plan.icon className={`w-8 h-8 text-${plan.color}-400`} />
                      </div>
                      <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                      <p className="text-slate-400 mb-4">{plan.description}</p>
                      <div className="text-4xl font-bold text-white mb-2">
                        {formatPrice(billingCycle === "monthly" ? plan.monthlyPrice : plan.annualPrice)}
                        {plan.monthlyPrice > 0 && (
                          <span className="text-lg text-slate-400">
                            /{billingCycle === "monthly" ? "month" : "year"}
                          </span>
                        )}
                      </div>
                      {billingCycle === "annual" && plan.monthlyPrice > 0 && (
                        <p className="text-slate-400 text-sm">
                          ${Math.round(plan.annualPrice / 12).toLocaleString()}/month billed annually
                        </p>
                      )}
                    </div>

                    <ul className="space-y-3 mb-8">
                      {plan.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-start text-slate-300">
                          <CheckCircle className="w-5 h-5 text-emerald-400 mr-3 mt-0.5 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                      {plan.limitations.map((limitation, limitationIndex) => (
                        <li key={limitationIndex} className="flex items-start text-slate-400">
                          <X className="w-5 h-5 text-slate-500 mr-3 mt-0.5 flex-shrink-0" />
                          {limitation}
                        </li>
                      ))}
                    </ul>

                    <Button 
                      className={`w-full ${
                        plan.popular
                          ? "bg-emerald-600 hover:bg-emerald-700"
                          : plan.name === "Starter"
                          ? "bg-blue-600 hover:bg-blue-700"
                          : "bg-amber-600 hover:bg-amber-700"
                      } text-white`}
                    >
                      {plan.name === "Starter" ? "Get Started Free" : 
                       plan.name === "Enterprise" ? "Contact Sales" : "Start Free Trial"}
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 px-4 bg-slate-800/30">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-bold text-white mb-4">Frequently Asked Questions</h2>
            <p className="text-slate-300 max-w-2xl mx-auto text-lg">
              Everything you need to know about our pricing and plans.
            </p>
          </motion.div>

          <div className="max-w-3xl mx-auto space-y-6">
            {[
              {
                question: "Can I change plans anytime?",
                answer: "Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately and we'll prorate the billing."
              },
              {
                question: "Is there a free trial?",
                answer: "Yes! Professional and Enterprise plans come with a 14-day free trial. No credit card required to start."
              },
              {
                question: "What payment methods do you accept?",
                answer: "We accept all major credit cards, UPI, net banking, and bank transfers for annual plans."
              },
              {
                question: "Is my data secure?",
                answer: "Absolutely. We use bank-grade encryption and security measures to protect your sensitive business data."
              }
            ].map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="bg-slate-800/50 border-slate-700/50">
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold text-white mb-2">{faq.question}</h3>
                    <p className="text-slate-300">{faq.answer}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 border-t border-slate-800 py-16 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-16 h-16 relative">
                  <img
                    src="/tenxcfo-dark.png"
                    alt="TenxCFO Logo"
                    width={64}
                    height={64}
                    className="w-full h-full object-contain logo-theme-aware"
                  />
                </div>
              </div>
              <p className="text-slate-400 mb-4">
                Connecting SMEs with smart investors through data-driven insights and automated financial scoring.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-white mb-4">Platform</h4>
              <ul className="space-y-2 text-slate-400">
                <li><Link href="/sme" className="hover:text-white transition-colors">For SMEs</Link></li>
                <li><Link href="/investor" className="hover:text-white transition-colors">For Investors</Link></li>
                <li><Link href="/consultant" className="hover:text-white transition-colors">For Consultants</Link></li>
                <li><Link href="/10x-growth-hack" className="hover:text-white transition-colors">10X Growth</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-white mb-4">Resources</h4>
              <ul className="space-y-2 text-slate-400">
                <li><Link href="/pricing" className="hover:text-white transition-colors">Pricing</Link></li>
                <li><a href="#" className="hover:text-white transition-colors">Documentation</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Case Studies</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Blog</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-white mb-4">Company</h4>
              <ul className="space-y-2 text-slate-400">
                <li><a href="#" className="hover:text-white transition-colors">About</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Contact</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Privacy</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Terms</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-slate-800 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-slate-400 text-sm">&copy; 2024 CFOx. All rights reserved.</p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-slate-400 hover:text-white text-sm transition-colors">Privacy Policy</a>
              <a href="#" className="text-slate-400 hover:text-white text-sm transition-colors">Terms of Service</a>
              <a href="#" className="text-slate-400 hover:text-white text-sm transition-colors">Cookie Policy</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
