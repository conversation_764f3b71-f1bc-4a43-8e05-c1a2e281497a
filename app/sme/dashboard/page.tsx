"use client";

import AppHeader from "@/components/navigation/AppHeader";
import { useAuthStore } from "@/stores/authStore";
import {
    BarChart3,
    FileText,
    Target,
    TrendingUp,
    Upload,
    Users
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";

// Dashboard components
const DashboardLayout = require("@/components/dashboard/DashboardLayout").default;
const MetricCard = require("@/components/dashboard/MetricCard").default;
const AreaChart = require("@/components/dashboard/AreaChart").default;
const DataTable = require("@/components/dashboard/DataTable").default;

export default function SMEDashboard() {
  const router = useRouter();
  const { logout, user } = useAuthStore();

  const handleLogout = () => {
    logout();
    router.push('/');
  };
  // Mock data - in real app, this would come from API
  const financialScore = 78;
  const documentsUploaded = 8;
  const totalDocuments = 12;
  const investorInterest = 15;
  const profileCompletion = 85;

  const recentActivity = [
    { id: 1, type: "document", message: "Financial statements uploaded", time: "2 hours ago", status: "success" },
    { id: 2, type: "investor", message: "3 new investors viewed your profile", time: "5 hours ago", status: "info" },
    { id: 3, type: "score", message: "Financial health score improved by 5 points", time: "1 day ago", status: "success" },
    { id: 4, type: "meeting", message: "Advisor call scheduled for tomorrow", time: "2 days ago", status: "pending" }
  ];

  const actionItems = [
    { id: 1, task: "Upload bank statements (last 12 months)", priority: "high", completed: false },
    { id: 2, task: "Complete business profile information", priority: "medium", completed: false },
    { id: 3, task: "Schedule advisor consultation call", priority: "low", completed: true },
    { id: 4, task: "Review investor interest notifications", priority: "medium", completed: false }
  ];

  // Enhanced dashboard data
  const [chartFilter, setChartFilter] = useState("Last 30 days");

  const revenueData = [
    { name: "Jan", value: 45000, value2: 38000 },
    { name: "Feb", value: 52000, value2: 42000 },
    { name: "Mar", value: 48000, value2: 40000 },
    { name: "Apr", value: 61000, value2: 48000 },
    { name: "May", value: 55000, value2: 45000 },
    { name: "Jun", value: 67000, value2: 52000 }
  ];

  const documentsTableData = [
    {
      id: "1",
      header: "Financial Statements",
      sectionType: "Financial",
      status: "Done",
      target: "Q4 2023",
      limit: "5",
      reviewer: "System"
    },
    {
      id: "2",
      header: "Tax Returns",
      sectionType: "Tax",
      status: "In Progress",
      target: "2023",
      limit: "24",
      reviewer: "Pending"
    },
    {
      id: "3",
      header: "Business Plan",
      sectionType: "Strategy",
      status: "Done",
      target: "2024",
      limit: "13",
      reviewer: "Approved"
    },
    {
      id: "4",
      header: "Legal Documents",
      sectionType: "Legal",
      status: "In Progress",
      target: "Current",
      limit: "23",
      reviewer: "Review"
    }
  ];

  const documentsTableColumns = [
    { key: "header", label: "Document", width: "25%" },
    { key: "sectionType", label: "Type", width: "20%" },
    { key: "status", label: "Status", width: "15%" },
    { key: "target", label: "Period", width: "15%" },
    { key: "limit", label: "Pages", width: "10%" },
    { key: "reviewer", label: "Status", width: "15%" }
  ];

  return (
    <>
      <AppHeader variant="sme" />
      <DashboardLayout
        title={`Welcome back, ${user?.companyName || user?.name || 'User'}`}
        subtitle="Here's your business performance overview"
        userBadge={{ text: `Profile ${profileCompletion}% Complete`, variant: "success" }}
      >

        {/* Key Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <MetricCard
            title="Financial Health Score"
            value={`${financialScore}/100`}
            change={{ value: "+5", type: "increase" }}
            icon={TrendingUp}
            iconColor="text-blue-600"
            iconBgColor="bg-blue-50"
            badge={{ text: "+5 this month", variant: "success" }}
          />
          <MetricCard
            title="Documents Uploaded"
            value={`${documentsUploaded}/${totalDocuments}`}
            change={{ value: "67%", type: "increase" }}
            icon={FileText}
            iconColor="text-emerald-600"
            iconBgColor="bg-emerald-50"
            description="Upload progress"
          />
          <MetricCard
            title="Investor Interest"
            value={investorInterest}
            change={{ value: "+3 today", type: "increase" }}
            icon={Users}
            iconColor="text-amber-600"
            iconBgColor="bg-amber-50"
            description="High interest level"
          />
          <MetricCard
            title="Profile Completion"
            value={`${profileCompletion}%`}
            change={{ value: "+10%", type: "increase" }}
            icon={Target}
            iconColor="text-purple-600"
            iconBgColor="bg-purple-50"
            description="Almost ready"
          />
        </div>

        {/* Revenue Chart and Summary */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          <div className="lg:col-span-2">
            <AreaChart
              title="Revenue Trend"
              data={revenueData}
              dataKey="value"
              dataKey2="value2"
              color="#3b82f6"
              color2="#10b981"
              height={250}
              timeFilters={["Last 6 months", "Last 3 months", "Last month"]}
              selectedFilter={chartFilter}
              onFilterChange={setChartFilter}
            />
          </div>
          <div>
            <MetricCard
              title="Monthly Revenue"
              value="$67,000"
              change={{ value: "+22%", type: "increase" }}
              icon={BarChart3}
              iconColor="text-green-600"
              iconBgColor="bg-green-50"
              description="Strong growth this month"
              className="h-full"
            />
          </div>
        </div>

        <DataTable
          title="Document Status"
          columns={documentsTableColumns}
          data={documentsTableData}
          actions={[
            {
              label: "Upload Documents",
              onClick: () => console.log("Upload documents"),
              icon: <Upload className="w-4 h-4 mr-1" />
            }
          ]}
          pagination={{
            currentPage: 1,
            totalPages: 3,
            onPageChange: (page) => console.log("Page:", page),
            itemsPerPage: 10
          }}
        />
      </DashboardLayout>
    </>
  );
}
