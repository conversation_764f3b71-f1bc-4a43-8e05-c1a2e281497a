"use client";

import AppHeader from "@/components/navigation/AppHeader";
import { useAuthStore } from "@/stores/authStore";
import {
    ArrowRight,
    BarChart3,
    Building,
    DollarSign,
    Eye,
    TrendingUp
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";

// Dashboard components
const DashboardLayout = require("@/components/dashboard/DashboardLayout").default;
const MetricCard = require("@/components/dashboard/MetricCard").default;
const AreaChart = require("@/components/dashboard/AreaChart").default;
const DataTable = require("@/components/dashboard/DataTable").default;

export default function InvestorDashboard() {
  const router = useRouter();
  const { logout, user } = useAuthStore();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFilter, setSelectedFilter] = useState("all");

  const handleLogout = () => {
    logout();
    router.push('/');
  };

  // Mock data - in real app, this would come from API
  const portfolioStats = {
    totalInvestments: 12,
    totalInvested: "$3.1M",
    activeDeals: 8,
    avgReturn: "18.5%"
  };

  // Enhanced dashboard data
  const [chartFilter, setChartFilter] = useState("Last 30 days");

  const chartData = [
    { name: "Apr 4", value: 1200, value2: 800 },
    { name: "Apr 10", value: 1350, value2: 900 },
    { name: "Apr 15", value: 1100, value2: 750 },
    { name: "Apr 21", value: 1800, value2: 1200 },
    { name: "Apr 28", value: 1600, value2: 1100 },
    { name: "May 5", value: 2100, value2: 1400 },
    { name: "May 12", value: 1900, value2: 1300 },
    { name: "May 19", value: 2400, value2: 1600 },
    { name: "May 22", value: 2200, value2: 1500 },
    { name: "May 28", value: 2800, value2: 1900 },
    { name: "Jun 3", value: 2600, value2: 1800 },
    { name: "Jun 6", value: 3100, value2: 2100 },
    { name: "Jun 14", value: 2900, value2: 2000 },
    { name: "Jun 21", value: 3400, value2: 2300 },
    { name: "Jun 30", value: 3200, value2: 2200 }
  ];

  const tableData = [
    {
      id: "1",
      header: "TechCorp Solutions",
      sectionType: "Technology",
      status: "In Progress",
      target: "650K",
      limit: "5",
      reviewer: "John Smith"
    },
    {
      id: "2",
      header: "GreenEnergy Ltd",
      sectionType: "Renewable Energy",
      status: "Done",
      target: "1.6M",
      limit: "24",
      reviewer: "Sarah Johnson"
    },
    {
      id: "3",
      header: "FinTech Innovations",
      sectionType: "Financial Services",
      status: "Done",
      target: "850K",
      limit: "13",
      reviewer: "Mike Wilson"
    },
    {
      id: "4",
      header: "HealthTech Solutions",
      sectionType: "Healthcare",
      status: "In Progress",
      target: "1.2M",
      limit: "23",
      reviewer: "Emily Davis"
    }
  ];

  const tableColumns = [
    { key: "header", label: "Company", width: "25%" },
    { key: "sectionType", label: "Industry", width: "20%" },
    { key: "status", label: "Status", width: "15%" },
    { key: "target", label: "Target", width: "15%" },
    { key: "limit", label: "Limit", width: "10%" },
    { key: "reviewer", label: "Reviewer", width: "15%" }
  ];

  const dealPipeline = [
    {
      id: 1,
      companyName: "TechCorp Solutions",
      industry: "Technology",
      location: "Bangalore",
      fundingGoal: "$650K",
      financialScore: 78,
      yearEstablished: 2020,
      employees: "25-50",
      revenue: "$3.1M",
      status: "new",
      description: "AI-powered customer service automation platform",
      tags: ["SaaS", "AI", "B2B"],
      interested: 15,
      viewed: 234
    },
    {
      id: 2,
      companyName: "GreenEnergy Pvt Ltd",
      industry: "Renewable Energy",
      location: "Pune",
      fundingGoal: "$1.6M",
      financialScore: 85,
      yearEstablished: 2018,
      employees: "50-100",
      revenue: "$7.5M",
      status: "trending",
      description: "Solar panel manufacturing and installation services",
      tags: ["CleanTech", "Manufacturing", "B2B"],
      interested: 28,
      viewed: 456
    },
    {
      id: 3,
      companyName: "HealthTech Innovations",
      industry: "Healthcare",
      location: "Mumbai",
      fundingGoal: "$975K",
      financialScore: 72,
      yearEstablished: 2021,
      employees: "10-25",
      revenue: "$2.3M",
      status: "hot",
      description: "Telemedicine platform for rural healthcare access",
      tags: ["HealthTech", "B2C", "Social Impact"],
      interested: 22,
      viewed: 189
    }
  ];

  const recentActivity = [
    { id: 1, type: "view", message: "You viewed TechCorp Solutions", time: "2 hours ago" },
    { id: 2, type: "interest", message: "You showed interest in GreenEnergy Pvt Ltd", time: "5 hours ago" },
    { id: 3, type: "message", message: "New message from HealthTech Innovations", time: "1 day ago" },
    { id: 4, type: "match", message: "3 new deals match your criteria", time: "2 days ago" }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "new": return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      case "trending": return "bg-emerald-500/20 text-emerald-400 border-emerald-500/30";
      case "hot": return "bg-red-500/20 text-red-400 border-red-500/30";
      default: return "bg-slate-500/20 text-slate-400 border-slate-500/30";
    }
  };

  return (
    <>
      <AppHeader variant="investor" />
      <DashboardLayout
        title={`Welcome back, ${user?.name || 'Investor'}`}
        subtitle="Discover and manage your investment opportunities"
        userBadge={{ text: "Professional Investor", variant: "success" }}
      >

        {/* Portfolio Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <MetricCard
            title="Total Revenue"
            value="$1,250.00"
            change={{ value: "+12.5%", type: "increase" }}
            icon={DollarSign}
            iconColor="text-emerald-600"
            iconBgColor="bg-emerald-50"
            description="Trending up this month"
          />
          <MetricCard
            title="New Customers"
            value="1,234"
            change={{ value: "-20%", type: "decrease" }}
            icon={Building}
            iconColor="text-blue-600"
            iconBgColor="bg-blue-50"
            description="Down 20% this period"
          />
          <MetricCard
            title="Active Accounts"
            value="45,678"
            change={{ value: "+12.5%", type: "increase" }}
            icon={BarChart3}
            iconColor="text-purple-600"
            iconBgColor="bg-purple-50"
            description="Strong user retention"
          />
          <MetricCard
            title="Growth Rate"
            value="4.5%"
            change={{ value: "+4.3%", type: "increase" }}
            icon={TrendingUp}
            iconColor="text-orange-600"
            iconBgColor="bg-orange-50"
            description="Steady performance increase"
          />
        </div>

        {/* Chart and Table Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          <div className="lg:col-span-2">
            <AreaChart
              title="Total Visitors"
              data={chartData}
              dataKey="value"
              dataKey2="value2"
              color="#3b82f6"
              color2="#10b981"
              height={250}
              timeFilters={["Last 3 months", "Last 30 days", "Last 7 days"]}
              selectedFilter={chartFilter}
              onFilterChange={setChartFilter}
            />
          </div>
          <div>
            <MetricCard
              title="Total Visitors"
              value="45,678"
              change={{ value: "+12.5%", type: "increase" }}
              icon={Eye}
              iconColor="text-blue-600"
              iconBgColor="bg-blue-50"
              description="Total for the last 3 months"
              className="h-full"
            />
          </div>
        </div>

        <DataTable
          title="Investment Pipeline"
          columns={tableColumns}
          data={tableData}
          actions={[
            {
              label: "Add Section",
              onClick: () => console.log("Add section"),
              icon: <ArrowRight className="w-4 h-4 mr-1" />
            }
          ]}
          pagination={{
            currentPage: 1,
            totalPages: 7,
            onPageChange: (page) => console.log("Page:", page),
            itemsPerPage: 10
          }}
        />
      </DashboardLayout>
    </>
  );
}
