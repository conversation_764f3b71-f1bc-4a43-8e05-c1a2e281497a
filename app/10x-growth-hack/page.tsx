"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import Logo from "@/components/ui/logo";
import { motion } from "framer-motion";
import { ArrowR<PERSON>, CheckCircle, Clock, Shield, Star, Target, TrendingUp, Users, Zap } from "lucide-react";
import Link from "next/link";

export default function GrowthHackPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <header className="border-b border-slate-700/50 bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Logo href="/" size="md" showText={false} />
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/sme" className="text-slate-300 hover:text-white transition-colors">For SMEs</Link>
            <Link href="/investor" className="text-slate-300 hover:text-white transition-colors">For Investors</Link>
            <Link href="/consultant" className="text-slate-300 hover:text-white transition-colors">For Consultants</Link>
            <Link href="/10x-growth-hack" className="text-white font-semibold border-b-2 border-amber-400">10X Growth</Link>
            <Link href="/pricing" className="text-slate-300 hover:text-white transition-colors">Pricing</Link>
            <Link href="/auth/signin" className="text-slate-300 hover:text-white transition-colors">Log in</Link>
            <Link href="/auth/signup">
              <Button className="bg-white text-slate-900 hover:bg-slate-100 rounded-full px-6">
                Sign up
              </Button>
            </Link>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Badge className="mb-6 px-6 py-2 text-sm font-medium bg-amber-500/10 text-amber-300 border-amber-500/20">
              🎯 Exclusive Program
            </Badge>
            <h1 className="text-5xl sm:text-6xl font-bold text-white mb-8 leading-tight">
              10X Growth Hack
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-amber-400 to-orange-400">
                Accelerator Program
              </span>
            </h1>
            <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto leading-relaxed">
              An exclusive 90-day intensive program designed to accelerate your business growth by 10X. 
              Get personalized strategies, expert mentorship, and direct investor connections.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="px-8 py-4 text-lg bg-amber-600 hover:bg-amber-700 text-white rounded-full">
                Apply for Program
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button variant="outline" size="lg" className="px-8 py-4 text-lg border-white/30 text-white hover:bg-white/10 rounded-full">
                View Success Stories
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Program Overview */}
      <section className="py-20 px-4 bg-slate-800/30">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-bold text-white mb-4">What Makes This Program Special?</h2>
            <p className="text-slate-300 max-w-2xl mx-auto text-lg">
              Unlike generic business programs, our 10X Growth Hack is tailored specifically for SMEs ready to scale rapidly.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* Personalized Strategy */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <Card className="h-full bg-slate-800/50 border-slate-700/50 hover:border-amber-500/50 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-amber-500/20 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <Target className="w-8 h-8 text-amber-400" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-4">Personalized Growth Strategy</h3>
                  <p className="text-slate-300 mb-6">
                    Custom-built growth plan based on your business model, market position, and financial health score.
                  </p>
                  <ul className="space-y-2 text-sm text-slate-400 text-left">
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-emerald-400 mr-2" />Deep business analysis</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-emerald-400 mr-2" />Market opportunity mapping</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-emerald-400 mr-2" />90-day action roadmap</li>
                  </ul>
                </CardContent>
              </Card>
            </motion.div>

            {/* Expert Mentorship */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card className="h-full bg-slate-800/50 border-slate-700/50 hover:border-blue-500/50 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-blue-500/20 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <Users className="w-8 h-8 text-blue-400" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-4">1-on-1 Expert Mentorship</h3>
                  <p className="text-slate-300 mb-6">
                    Weekly sessions with industry experts who have scaled businesses from startup to IPO.
                  </p>
                  <ul className="space-y-2 text-sm text-slate-400 text-left">
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-emerald-400 mr-2" />Weekly mentor calls</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-emerald-400 mr-2" />Industry-specific guidance</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-emerald-400 mr-2" />24/7 Slack support</li>
                  </ul>
                </CardContent>
              </Card>
            </motion.div>

            {/* Investor Network */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <Card className="h-full bg-slate-800/50 border-slate-700/50 hover:border-emerald-500/50 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-emerald-500/20 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <TrendingUp className="w-8 h-8 text-emerald-400" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-4">Direct Investor Access</h3>
                  <p className="text-slate-300 mb-6">
                    Exclusive access to our network of 500+ verified investors actively looking for opportunities.
                  </p>
                  <ul className="space-y-2 text-sm text-slate-400 text-left">
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-emerald-400 mr-2" />Investor pitch preparation</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-emerald-400 mr-2" />Direct introductions</li>
                    <li className="flex items-center"><CheckCircle className="w-4 h-4 text-emerald-400 mr-2" />Funding negotiation support</li>
                  </ul>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Program Timeline */}
      <section className="py-20 px-4">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-bold text-white mb-4">90-Day Transformation Journey</h2>
            <p className="text-slate-300 max-w-2xl mx-auto text-lg">
              A structured approach to achieving 10X growth in just 90 days.
            </p>
          </motion.div>

          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  phase: "Phase 1",
                  title: "Foundation & Analysis",
                  duration: "Days 1-30",
                  description: "Deep dive into your business, market analysis, and growth strategy development.",
                  icon: Shield,
                  color: "blue"
                },
                {
                  phase: "Phase 2", 
                  title: "Implementation & Optimization",
                  duration: "Days 31-60",
                  description: "Execute growth strategies, optimize operations, and prepare for scaling.",
                  icon: Zap,
                  color: "emerald"
                },
                {
                  phase: "Phase 3",
                  title: "Scale & Investment",
                  duration: "Days 61-90",
                  description: "Scale operations, connect with investors, and secure funding for continued growth.",
                  icon: TrendingUp,
                  color: "amber"
                }
              ].map((phase, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="text-center"
                >
                  <div className={`w-16 h-16 bg-${phase.color}-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4`}>
                    <phase.icon className={`w-8 h-8 text-${phase.color}-400`} />
                  </div>
                  <div className={`text-sm font-bold text-${phase.color}-400 mb-2`}>{phase.phase}</div>
                  <h3 className="text-lg font-semibold text-white mb-2">{phase.title}</h3>
                  <div className="text-sm text-slate-400 mb-3">{phase.duration}</div>
                  <p className="text-slate-400 text-sm">{phase.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Success Metrics */}
      <section className="py-20 px-4 bg-slate-800/30">
        <div className="container mx-auto text-center">
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-bold text-white mb-4">Program Results</h2>
            <p className="text-slate-300 max-w-2xl mx-auto text-lg mb-12">
              Real results from businesses that completed our 10X Growth Hack program.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-center"
            >
              <div className="text-4xl font-bold text-amber-400 mb-2">10X</div>
              <div className="text-slate-300">Average Revenue Growth</div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-center"
            >
              <div className="text-4xl font-bold text-emerald-400 mb-2">85%</div>
              <div className="text-slate-300">Secured Funding</div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="text-center"
            >
              <div className="text-4xl font-bold text-blue-400 mb-2">90</div>
              <div className="text-slate-300">Days to Results</div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="text-center"
            >
              <div className="text-4xl font-bold text-purple-400 mb-2">50+</div>
              <div className="text-slate-300">Success Stories</div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Qualification Criteria */}
      <section className="py-20 px-4">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-bold text-white mb-4">Are You Qualified?</h2>
            <p className="text-slate-300 max-w-2xl mx-auto text-lg">
              This exclusive program is designed for serious entrepreneurs ready to scale rapidly.
            </p>
          </motion.div>

          <div className="max-w-4xl mx-auto">
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardContent className="p-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-xl font-bold text-emerald-400 mb-4 flex items-center">
                      <CheckCircle className="w-6 h-6 mr-2" />
                      You're a Perfect Fit If:
                    </h3>
                    <ul className="space-y-3 text-slate-300">
                      <li className="flex items-start">
                        <Star className="w-5 h-5 text-emerald-400 mr-3 mt-0.5 flex-shrink-0" />
                        Annual revenue of $650K+ and growing
                      </li>
                      <li className="flex items-start">
                        <Star className="w-5 h-5 text-emerald-400 mr-3 mt-0.5 flex-shrink-0" />
                        Financial health score of 60+ on CFOx
                      </li>
                      <li className="flex items-start">
                        <Star className="w-5 h-5 text-emerald-400 mr-3 mt-0.5 flex-shrink-0" />
                        Committed to 10+ hours/week for 90 days
                      </li>
                      <li className="flex items-start">
                        <Star className="w-5 h-5 text-emerald-400 mr-3 mt-0.5 flex-shrink-0" />
                        Ready to implement aggressive growth strategies
                      </li>
                      <li className="flex items-start">
                        <Star className="w-5 h-5 text-emerald-400 mr-3 mt-0.5 flex-shrink-0" />
                        Open to external investment/partnerships
                      </li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-red-400 mb-4 flex items-center">
                      <Clock className="w-6 h-6 mr-2" />
                      This Program Isn't For You If:
                    </h3>
                    <ul className="space-y-3 text-slate-300">
                      <li className="flex items-start">
                        <span className="w-5 h-5 text-red-400 mr-3 mt-0.5 flex-shrink-0">✗</span>
                        You're looking for quick fixes or shortcuts
                      </li>
                      <li className="flex items-start">
                        <span className="w-5 h-5 text-red-400 mr-3 mt-0.5 flex-shrink-0">✗</span>
                        Your business is pre-revenue or struggling
                      </li>
                      <li className="flex items-start">
                        <span className="w-5 h-5 text-red-400 mr-3 mt-0.5 flex-shrink-0">✗</span>
                        You can't commit significant time and resources
                      </li>
                      <li className="flex items-start">
                        <span className="w-5 h-5 text-red-400 mr-3 mt-0.5 flex-shrink-0">✗</span>
                        You're not ready for rapid change and growth
                      </li>
                      <li className="flex items-start">
                        <span className="w-5 h-5 text-red-400 mr-3 mt-0.5 flex-shrink-0">✗</span>
                        You prefer traditional, slow-growth approaches
                      </li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Application CTA */}
      <section className="py-20 px-4 bg-gradient-to-r from-amber-600 to-orange-600">
        <div className="container mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            <Badge className="mb-4 bg-white/20 text-white border-white/30">
              🚀 Limited Spots Available
            </Badge>
            <h2 className="text-4xl font-bold text-white mb-4">Ready to 10X Your Business?</h2>
            <p className="text-xl text-amber-100 mb-8 max-w-2xl mx-auto">
              Only 20 businesses are accepted per cohort. Applications are reviewed within 48 hours.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-amber-600 hover:bg-amber-50 text-lg px-8 py-4 rounded-full">
                Apply Now - $1,299
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Link href="/discovery-call">
                <Button variant="outline" size="lg" className="border-white text-white hover:bg-white/10 text-lg px-8 py-4 rounded-full">
                  Schedule Discovery Call
                </Button>
              </Link>
            </div>
            <p className="text-amber-100 text-sm mt-4 opacity-80">
              * 100% money-back guarantee if you don't see measurable results in 90 days
            </p>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 border-t border-slate-800 py-16 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-16 h-16 relative">
                  <img
                    src="/tenxcfo-dark.png"
                    alt="TenxCFO Logo"
                    width={64}
                    height={64}
                    className="w-full h-full object-contain logo-theme-aware"
                  />
                </div>
              </div>
              <p className="text-slate-400 mb-4">
                Accelerating SME growth through data-driven insights and expert mentorship.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-white mb-4">Platform</h4>
              <ul className="space-y-2 text-slate-400">
                <li><Link href="/sme" className="hover:text-white transition-colors">For SMEs</Link></li>
                <li><Link href="/investor" className="hover:text-white transition-colors">For Investors</Link></li>
                <li><Link href="/consultant" className="hover:text-white transition-colors">For Consultants</Link></li>
                <li><Link href="/pricing" className="hover:text-white transition-colors">Pricing</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-white mb-4">Programs</h4>
              <ul className="space-y-2 text-slate-400">
                <li><Link href="/10x-growth-hack" className="hover:text-white transition-colors">10X Growth Hack</Link></li>
                <li><a href="#" className="hover:text-white transition-colors">Success Stories</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Mentorship</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Resources</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-white mb-4">Company</h4>
              <ul className="space-y-2 text-slate-400">
                <li><a href="#" className="hover:text-white transition-colors">About</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Contact</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Privacy</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Terms</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-slate-800 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-slate-400 text-sm">&copy; 2024 CFOx. All rights reserved.</p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-slate-400 hover:text-white text-sm transition-colors">Privacy Policy</a>
              <a href="#" className="text-slate-400 hover:text-white text-sm transition-colors">Terms of Service</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
