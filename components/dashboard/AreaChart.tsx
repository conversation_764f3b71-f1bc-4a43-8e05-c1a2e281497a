"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  AreaChart as RechartsAreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Re<PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>
} from "recharts";

interface ChartDataPoint {
  name: string;
  value: number;
  value2?: number;
  [key: string]: any;
}

interface AreaChartProps {
  title: string;
  data: ChartDataPoint[];
  dataKey: string;
  dataKey2?: string;
  color?: string;
  color2?: string;
  height?: number;
  showGrid?: boolean;
  showLegend?: boolean;
  timeFilters?: string[];
  selectedFilter?: string;
  onFilterChange?: (filter: string) => void;
  className?: string;
}

export default function AreaChart({
  title,
  data,
  dataKey,
  dataKey2,
  color = "#3b82f6",
  color2 = "#10b981",
  height = 300,
  showGrid = true,
  showLegend = false,
  timeFilters = ["Last 7 days", "Last 30 days", "Last 3 months"],
  selectedFilter = "Last 30 days",
  onFilterChange,
  className
}: AreaChartProps) {
  return (
    <Card className={`bg-white border-slate-200 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base font-semibold text-slate-900">{title}</CardTitle>
          {timeFilters && (
            <div className="flex gap-1">
              {timeFilters.map((filter) => (
                <Badge
                  key={filter}
                  variant={filter === selectedFilter ? "default" : "outline"}
                  className={`cursor-pointer text-xs px-2 py-1 ${
                    filter === selectedFilter
                      ? "bg-slate-900 text-white"
                      : "bg-transparent text-slate-600 border-slate-300 hover:bg-slate-50"
                  }`}
                  onClick={() => onFilterChange?.(filter)}
                >
                  {filter}
                </Badge>
              ))}
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <ResponsiveContainer width="100%" height={height}>
          <RechartsAreaChart data={data} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
            {showGrid && (
              <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
            )}
            <XAxis
              dataKey="name"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#64748b" }}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#64748b" }}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: "white",
                border: "1px solid #e2e8f0",
                borderRadius: "8px",
                boxShadow: "0 4px 6px -1px rgb(0 0 0 / 0.1)",
              }}
              labelStyle={{ color: "#334155" }}
            />
            {showLegend && <Legend />}
            <Area
              type="monotone"
              dataKey={dataKey}
              stroke={color}
              fill={color}
              fillOpacity={0.1}
              strokeWidth={2}
            />
            {dataKey2 && (
              <Area
                type="monotone"
                dataKey={dataKey2}
                stroke={color2}
                fill={color2}
                fillOpacity={0.1}
                strokeWidth={2}
              />
            )}
          </RechartsAreaChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}
