"use client";

import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";

interface DashboardLayoutProps {
  title: string;
  subtitle: string;
  userBadge?: {
    text: string;
    variant?: "default" | "success" | "warning" | "destructive";
  };
  children: React.ReactNode;
  className?: string;
}

export default function DashboardLayout({
  title,
  subtitle,
  userBadge,
  children,
  className = ""
}: DashboardLayoutProps) {
  const getBadgeVariant = () => {
    switch (userBadge?.variant) {
      case "success":
        return "bg-emerald-500/10 text-emerald-600 border-emerald-500/20";
      case "warning":
        return "bg-amber-500/10 text-amber-600 border-amber-500/20";
      case "destructive":
        return "bg-red-500/10 text-red-600 border-red-500/20";
      default:
        return "bg-blue-500/10 text-blue-600 border-blue-500/20";
    }
  };

  return (
    <div className={`min-h-screen bg-slate-50 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Header Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-6"
        >
          <div className="flex items-center justify-between mb-1">
            <div>
              <h1 className="text-2xl font-bold text-slate-900 mb-1">{title}</h1>
              <p className="text-sm text-slate-600">{subtitle}</p>
            </div>
            {userBadge && (
              <Badge className={`text-xs font-medium ${getBadgeVariant()}`}>
                {userBadge.text}
              </Badge>
            )}
          </div>
        </motion.div>

        {/* Dashboard Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="space-y-6"
        >
          {children}
        </motion.div>
      </div>
    </div>
  );
}
