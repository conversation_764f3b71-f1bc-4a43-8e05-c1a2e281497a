"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { ChevronLeft, ChevronRight, Plus } from "lucide-react";

interface TableColumn {
  key: string;
  label: string;
  width?: string;
  align?: "left" | "center" | "right";
}

interface TableRow {
  id: string;
  [key: string]: any;
}

interface DataTableProps {
  title: string;
  columns: TableColumn[];
  data: TableRow[];
  actions?: {
    label: string;
    onClick: () => void;
    icon?: React.ReactNode;
  }[];
  pagination?: {
    currentPage: number;
    totalPages: number;
    onPageChange: (page: number) => void;
    itemsPerPage: number;
  };
  className?: string;
}

export default function DataTable({
  title,
  columns,
  data,
  actions,
  pagination,
  className
}: DataTableProps) {
  const render<PERSON>ell<PERSON>ontent = (value: any, column: TableColumn) => {
    // Handle status badges
    if (column.key === "status") {
      const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
          case "done":
          case "completed":
          case "active":
            return "bg-emerald-500/10 text-emerald-600 border-emerald-500/20";
          case "in progress":
          case "pending":
            return "bg-amber-500/10 text-amber-600 border-amber-500/20";
          case "cancelled":
          case "inactive":
            return "bg-red-500/10 text-red-600 border-red-500/20";
          default:
            return "bg-slate-500/10 text-slate-600 border-slate-500/20";
        }
      };

      return (
        <Badge className={cn("text-xs font-medium", getStatusColor(value))}>
          <div className="w-1.5 h-1.5 rounded-full bg-current mr-1.5" />
          {value}
        </Badge>
      );
    }

    // Handle progress bars
    if (column.key === "progress" || column.key === "completion") {
      const percentage = typeof value === "number" ? value : parseInt(value) || 0;
      return (
        <div className="flex items-center gap-2">
          <Progress value={percentage} className="h-1.5 flex-1" />
          <span className="text-xs text-slate-500 min-w-[2rem]">{percentage}%</span>
        </div>
      );
    }

    // Handle numeric values with formatting
    if (typeof value === "number" && column.key.includes("amount")) {
      return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      }).format(value);
    }

    return value;
  };

  return (
    <Card className={cn("bg-white border-slate-200", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base font-semibold text-slate-900">{title}</CardTitle>
          {actions && (
            <div className="flex gap-2">
              {actions.map((action, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={action.onClick}
                  className="text-xs"
                >
                  {action.icon}
                  {action.label}
                </Button>
              ))}
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-slate-200">
                {columns.map((column) => (
                  <th
                    key={column.key}
                    className={cn(
                      "text-left py-2 text-xs font-medium text-slate-500 uppercase tracking-wider",
                      column.align === "center" && "text-center",
                      column.align === "right" && "text-right"
                    )}
                    style={{ width: column.width }}
                  >
                    {column.label}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-slate-100">
              {data.map((row) => (
                <tr key={row.id} className="hover:bg-slate-50/50">
                  {columns.map((column) => (
                    <td
                      key={column.key}
                      className={cn(
                        "py-3 text-sm text-slate-900",
                        column.align === "center" && "text-center",
                        column.align === "right" && "text-right"
                      )}
                    >
                      {renderCellContent(row[column.key], column)}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {pagination && (
          <div className="flex items-center justify-between mt-4 pt-3 border-t border-slate-200">
            <div className="text-xs text-slate-500">
              Page {pagination.currentPage} of {pagination.totalPages}
            </div>
            <div className="flex items-center gap-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => pagination.onPageChange(pagination.currentPage - 1)}
                disabled={pagination.currentPage === 1}
                className="w-8 h-8 p-0"
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => pagination.onPageChange(pagination.currentPage + 1)}
                disabled={pagination.currentPage === pagination.totalPages}
                className="w-8 h-8 p-0"
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
