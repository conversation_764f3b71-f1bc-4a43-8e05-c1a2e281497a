"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { LucideIcon, TrendingUp, TrendingDown } from "lucide-react";

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: {
    value: string;
    type: "increase" | "decrease" | "neutral";
    period?: string;
  };
  icon?: LucideIcon;
  iconColor?: string;
  iconBgColor?: string;
  description?: string;
  badge?: {
    text: string;
    variant?: "default" | "success" | "warning" | "destructive";
  };
  className?: string;
}

export default function MetricCard({
  title,
  value,
  change,
  icon: Icon,
  iconColor = "text-blue-400",
  iconBgColor = "bg-blue-500/10",
  description,
  badge,
  className
}: MetricCardProps) {
  const getTrendIcon = () => {
    if (!change) return null;
    
    if (change.type === "increase") {
      return <TrendingUp className="w-3 h-3" />;
    } else if (change.type === "decrease") {
      return <TrendingDown className="w-3 h-3" />;
    }
    return null;
  };

  const getTrendColor = () => {
    if (!change) return "";
    
    switch (change.type) {
      case "increase":
        return "text-emerald-400";
      case "decrease":
        return "text-red-400";
      default:
        return "text-slate-400";
    }
  };

  const getBadgeVariant = () => {
    switch (badge?.variant) {
      case "success":
        return "bg-emerald-500/10 text-emerald-400 border-emerald-500/20";
      case "warning":
        return "bg-amber-500/10 text-amber-400 border-amber-500/20";
      case "destructive":
        return "bg-red-500/10 text-red-400 border-red-500/20";
      default:
        return "bg-blue-500/10 text-blue-400 border-blue-500/20";
    }
  };

  return (
    <Card className={cn("bg-white border-slate-200 hover:shadow-sm transition-shadow", className)}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          {Icon && (
            <div className={cn("w-8 h-8 rounded-md flex items-center justify-center", iconBgColor)}>
              <Icon className={cn("w-4 h-4", iconColor)} />
            </div>
          )}
          {badge && (
            <Badge className={cn("text-xs font-medium", getBadgeVariant())}>
              {badge.text}
            </Badge>
          )}
        </div>
        
        <div className="space-y-1">
          <div className="text-2xl font-bold text-slate-900">{value}</div>
          <div className="text-sm text-slate-600">{title}</div>
          
          {change && (
            <div className={cn("flex items-center text-xs font-medium", getTrendColor())}>
              {getTrendIcon()}
              <span className="ml-1">{change.value}</span>
              {change.period && (
                <span className="ml-1 text-slate-500">{change.period}</span>
              )}
            </div>
          )}
          
          {description && (
            <div className="text-xs text-slate-500 mt-1">{description}</div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
